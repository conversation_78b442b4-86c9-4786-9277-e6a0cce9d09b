# 多智能体系统 - 基于Google ADK和MCP工具

这是一个基于Google Agent Development Kit (ADK) 构建的多智能体系统，集成了多个MCP (Model Context Protocol) 工具，实现了自动化的任务规划、执行、检查和错误处理流程。

## 系统架构

### 智能体组成

1. **任务规划智能体 (Agent1)**
   - 模型: gemini-1.5-flash-latest
   - 功能: 分析用户输入，制定详细任务规划和检查指标
   - MCP工具: ai_knowledge_bridge

2. **任务执行智能体 (Agent2)**
   - 模型: gemini-1.5-flash-latest
   - 功能: 执行具体任务，控制终端、桌面和文件系统操作
   - MCP工具: AutoOmni, desktop-commander

3. **任务检查智能体 (Agent3)**
   - 模型: gemini-1.5-flash-latest
   - 功能: 验证任务执行结果，检查是否达到完成指标

4. **错误处理智能体 (Agent4)**
   - 模型: gemini-1.5-flash-latest
   - 功能: 处理错误和不足，制定修复方案
   - MCP工具: ai_knowledge_bridge

### 工作流程

```
用户输入 → 任务规划 → 执行循环 → 完成
                ↓         ↑
            [执行 → 检查 → 错误处理]
```

## 安装和配置

### 1. 环境要求

- Python 3.8+
- Google ADK
- Node.js (用于某些MCP工具)

### 2. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 或者使用项目根目录的requirements.txt
pip install -r app/requirements.txt
```

### 3. 配置环境变量

复制环境变量模板：
```bash
cp app/.env.example app/.env
```

编辑 `app/.env` 文件，填入必要的配置：
```env
# Google API Key for Gemini models
GOOGLE_API_KEY=your_google_api_key_here

# MCP Server Paths (根据您的实际路径调整)
AI_KNOWLEDGE_BRIDGE_PATH=c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py
AUTO_OMNI_PATH=c:/dev/MCP/AutoOmni/auto_omni_mcp.py

# Application Settings
APP_NAME=multi_agent_system_app
USER_ID=user_1
SESSION_ID=session_001
```

### 4. MCP工具配置

确保以下MCP工具已正确安装和配置：

#### ai_knowledge_bridge
```json
{
  "ai_knowledge_bridge": {
    "runtime": "python",
    "command": "python",
    "args": [
      "c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py"
    ]
  }
}
```

#### AutoOmni
```json
{
  "auto-omni": {
    "runtime": "python",
    "command": "python",
    "args": [
      "c:/dev/MCP/AutoOmni/auto_omni_mcp.py"
    ],
    "port": 5001
  }
}
```

#### desktop-commander
```json
{
  "desktop-commander": {
    "command": "npx",
    "args": [
      "-y",
      "@wonderwhy-er/desktop-commander"
    ]
  }
}
```

## 使用方法

### 1. 启动系统

```bash
cd app
python main.py
```

### 2. 交互式使用

系统启动后，您可以输入任务需求，例如：

```
👤 您: 帮我创建一个Python项目，包含基本的目录结构和配置文件

👤 您: 安装并配置一个Web服务器，监听8080端口

👤 您: 备份我的文档文件夹到外部硬盘
```

### 3. 使用ADK Web界面

您也可以使用ADK的Web界面进行可视化交互：

```bash
# 从项目根目录运行
adk web
```

然后在浏览器中打开 `http://localhost:8000`，选择 "multi_agent_system_coordinator"。

## 系统特性

### 多智能体协作
- 各智能体专门化分工，提高效率和准确性
- 自动化的工作流程，减少人工干预
- 智能的错误处理和重试机制

### MCP工具集成
- **ai_knowledge_bridge**: 提供专业知识和最佳实践
- **AutoOmni**: 复杂系统操作和自动化
- **desktop-commander**: 桌面GUI操作和控制

### 安全性和可靠性
- 任务执行前的详细规划
- 执行后的严格验证
- 自动错误检测和修复
- 最大重试次数限制

## 项目结构

```
.
├── multi_agent_system/          # 多智能体系统核心
│   ├── __init__.py
│   ├── agent.py                 # 主协调器和工作流
│   └── agents/                  # 各个专门智能体
│       ├── __init__.py
│       ├── task_planner.py      # 任务规划智能体
│       ├── task_executor.py     # 任务执行智能体
│       ├── task_checker.py      # 任务检查智能体
│       └── error_handler.py     # 错误处理智能体
├── app/                         # 应用程序
│   ├── __init__.py
│   ├── main.py                  # 应用入口
│   ├── requirements.txt         # 应用依赖
│   ├── .env.example            # 环境变量模板
│   └── .env                    # 环境变量配置
├── mcp_stdio_server_agent/     # 原有的MCP示例
├── doc.md                      # ADK文档
├── requirements.txt            # 项目依赖
├── pyproject.toml             # 项目配置
└── README.md                  # 本文件
```

## 故障排除

### 常见问题

1. **API密钥错误**
   - 确保在`.env`文件中正确设置了`GOOGLE_API_KEY`
   - 验证API密钥的有效性和权限

2. **MCP工具连接失败**
   - 检查MCP工具的路径是否正确
   - 确保相关的Python脚本和Node.js包已安装

3. **依赖项问题**
   - 运行 `pip install -r requirements.txt` 重新安装依赖
   - 检查Python版本是否符合要求

### 调试模式

如需调试，可以在代码中添加更详细的日志输出，或使用ADK的内置调试功能。

## 扩展和定制

### 添加新的智能体
1. 在 `multi_agent_system/agents/` 目录下创建新的智能体文件
2. 在 `multi_agent_system/agents/__init__.py` 中导入新智能体
3. 在主工作流中集成新智能体

### 集成新的MCP工具
1. 在相应的智能体中添加新的MCPToolset配置
2. 更新智能体的指令以使用新工具
3. 在环境变量中添加必要的配置

## 许可证

本项目基于Apache License 2.0许可证开源。详见LICENSE文件。
