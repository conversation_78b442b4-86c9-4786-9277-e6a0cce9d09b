# 🚀 多智能体系统完整设置指南

## 当前状态分析

您遇到的问题说明：
1. ✅ **系统架构完全正常** - 多智能体协作、通信都工作正常
2. ❌ **MCP工具未启用** - 当前使用的是简化版本，没有调用MCP工具
3. ⚠️ **编码问题** - Windows GBK编码无法处理Unicode字符 `✗`

## 🛠️ 解决方案

### 方案1: 使用带MCP工具的完整版本

#### 1. 设置Google API密钥
```bash
# 获取API密钥: https://aistudio.google.com/app/apikey
# 设置环境变量 (Windows)
set GOOGLE_API_KEY=your_api_key_here

# 或者 (PowerShell)
$env:GOOGLE_API_KEY="your_api_key_here"

# 或者 (Git Bash)
export GOOGLE_API_KEY="your_api_key_here"
```

#### 2. 确保MCP工具可用
```bash
# 检查Python路径中的MCP工具
python c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py --help
python c:/dev/MCP/AutoOmni/auto_omni_mcp.py --help

# 安装desktop-commander
npm install -g @wonderwhy-er/desktop-commander
```

#### 3. 运行完整版系统
```bash
# 启动带MCP工具的完整版本
adk run mcp_multi_agent
```

### 方案2: 继续使用简化版本（推荐用于测试）

#### 1. 设置API密钥（同上）

#### 2. 运行简化版本
```bash
# 启动简化版本（已修复编码问题）
adk run simple_multi_agent
```

## 🔍 问题诊断

### 检查API密钥是否设置
```bash
# Windows CMD
echo %GOOGLE_API_KEY%

# PowerShell
echo $env:GOOGLE_API_KEY

# Git Bash
echo $GOOGLE_API_KEY
```

### 检查MCP工具状态
```bash
# 运行MCP检查脚本
python setup_mcp.py
```

### 检查系统功能
```bash
# 运行系统测试
python test_system.py
```

## 📋 版本对比

| 功能 | simple_multi_agent | mcp_multi_agent |
|------|-------------------|-----------------|
| 多智能体协作 | ✅ | ✅ |
| 中文支持 | ✅ | ✅ |
| 错误处理 | ✅ | ✅ |
| ai_knowledge_bridge | ❌ | ✅ |
| AutoOmni | ❌ | ✅ |
| desktop-commander | ❌ | ✅ |
| 设置复杂度 | 简单 | 中等 |

## 🎯 推荐步骤

### 立即可用（推荐）
1. 设置Google API密钥
2. 运行: `adk run simple_multi_agent`
3. 测试基本功能

### 完整功能
1. 设置Google API密钥
2. 确保MCP工具可用
3. 运行: `adk run mcp_multi_agent`
4. 享受完整的MCP工具功能

## 🔧 故障排除

### 编码错误
- **问题**: `UnicodeEncodeError: 'gbk' codec can't encode character '\u2717'`
- **解决**: 已在新版本中修复，使用 `[完成]/[未完成]` 替代Unicode字符

### MCP工具连接失败
- **检查**: MCP工具路径是否正确
- **解决**: 运行 `python setup_mcp.py` 检查和配置

### API调用失败
- **检查**: Google API密钥是否设置
- **解决**: 确保API密钥有效且有足够配额

## 🎉 测试建议

### 基础测试
```
输入: "帮我创建一个Python项目结构"
期望: 系统分析、规划、执行、检查的完整流程
```

### 高级测试（需要MCP工具）
```
输入: "帮我安装并配置一个Web服务器"
期望: 使用MCP工具进行实际的系统操作
```

---

选择适合您当前需求的方案，系统已经准备就绪！🚀
