#!/usr/bin/env python3
# Copyright 2025 Multi-Agent System Application
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import sys
import asyncio
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from google.adk.core.runner import Runner
from google.adk.core.session_service import InMemorySessionService
from google.adk import types

from multi_agent_system.agent import root_agent


async def call_agent_async(query: str, runner, user_id: str, session_id: str):
    """
    发送查询到智能体并处理响应
    """
    print(f"\n🤖 处理用户输入: {query}")
    print("=" * 50)
    
    # 格式化用户查询
    content = types.Content(role="user", parts=[types.Part(text=query)])
    
    final_response_text = ""
    
    # 运行智能体并处理事件
    async for event in runner.run_async(
        user_id=user_id, session_id=session_id, new_message=content
    ):
        # 处理不同类型的事件
        if hasattr(event, 'author') and event.author:
            print(f"📍 [{event.author}] ", end="")
            
        if hasattr(event, 'content') and event.content and event.content.parts:
            event_text = event.content.parts[0].text if event.content.parts[0].text else ""
            if event_text:
                print(f"{event_text}")
        
        # 检查是否是最终响应
        if event.is_final_response():
            if event.content and event.content.parts:
                final_response_text = event.content.parts[0].text
                
    return {"text": final_response_text}


async def interactive_session(runner, user_id: str, session_id: str):
    """
    运行交互式会话
    """
    print("\n" + "=" * 60)
    print("🚀 多智能体系统 - 基于Google ADK和MCP工具")
    print("=" * 60)
    print("💡 系统包含以下智能体:")
    print("   1. 任务规划智能体 - 分析需求并制定计划")
    print("   2. 任务执行智能体 - 执行具体操作")
    print("   3. 任务检查智能体 - 验证执行结果")
    print("   4. 错误处理智能体 - 处理错误和重新规划")
    print("\n📝 请输入您的任务需求，系统将自动协调各智能体完成任务")
    print("💬 输入 'exit' 或 'quit' 退出系统")
    print("=" * 60)
    
    while True:
        try:
            user_input = input("\n👤 您: ").strip()
            if user_input.lower() in ["exit", "quit", "退出"]:
                print("\n👋 感谢使用多智能体系统，再见！")
                break
                
            if not user_input:
                print("⚠️  请输入有效的任务需求")
                continue
                
            # 发送用户输入到智能体并获取响应
            result = await call_agent_async(user_input, runner, user_id, session_id)
            
            # 显示最终响应
            print(f"\n🎯 系统最终响应:")
            print("-" * 40)
            print(f"{result['text']}")
            print("-" * 40)
                    
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            print("🔄 请重试或输入新的任务需求")


async def main():
    """
    主函数
    """
    # 加载环境变量
    load_dotenv()
    
    # 配置应用参数
    APP_NAME = os.getenv("APP_NAME", "multi_agent_system_app")
    USER_ID = os.getenv("USER_ID", "user_1")
    SESSION_ID = os.getenv("SESSION_ID", "session_001")
    
    # 检查必要的环境变量
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ 错误: 请在.env文件中设置GOOGLE_API_KEY")
        print("💡 提示: 复制.env.example为.env并填入您的API密钥")
        return
    
    try:
        # 设置会话管理
        session_service = InMemorySessionService()
        session = session_service.create_session(
            app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
        )
        
        # 设置智能体运行器
        runner = Runner(
            agent=root_agent, app_name=APP_NAME, session_service=session_service
        )
        
        print("✅ 多智能体系统初始化成功")
        
        # 启动交互式会话
        await interactive_session(runner, USER_ID, SESSION_ID)
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        print("🔧 请检查配置和依赖项")


if __name__ == "__main__":
    asyncio.run(main())
