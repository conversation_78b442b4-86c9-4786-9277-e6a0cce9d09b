# Copyright 2025 Multi-Agent System with MCP Tools
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
from google.adk.agents.llm_agent import LlmAgent
from google.adk.agents.sequential_agent import SequentialAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset
from google.adk.tools.mcp_tool.mcp_toolset import StdioServerParameters

# 创建带MCP工具的智能体

# Agent1: 任务规划智能体 - 使用ai_knowledge_bridge
task_planner = LlmAgent(
    model='gemini-1.5-flash-latest',
    name='task_planner_agent',
    description='任务规划智能体，负责分析用户输入并制定详细的任务规划和检查指标',
    instruction="""
你是一个专业的任务规划智能体。你的主要职责是：

1. 接收用户输入的任务需求
2. 使用ai_knowledge_bridge工具获取专业知识和最佳实践
3. 将分析结果分为两个部分：
   - 详细任务规划部分：包含具体的执行步骤、所需工具、预期结果等
   - 检查指标部分：包含任务完成的验证标准、成功指标、质量要求等

输出格式：
请将结果分为两个明确的部分：
[任务规划]
- 步骤1: ...
- 步骤2: ...
...

[检查指标]  
- 指标1: ...
- 指标2: ...
...
    """,
    tools=[
        MCPToolset(
            connection_params=StdioServerParameters(
                command='python',
                args=[
                    'c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py'
                ],
            ),
        )
    ],
    output_key="task_planning_result"
)

# Agent2: 任务执行智能体 - 使用AutoOmni和desktop-commander
task_executor = LlmAgent(
    model='gemini-1.5-flash-latest',
    name='task_executor_agent',
    description='任务执行智能体，负责执行具体的任务步骤，包括终端操作、桌面操作和文件系统操作',
    instruction="""
你是一个专业的任务执行智能体。你的主要职责是：

1. 接收来自任务规划智能体的详细任务规划
2. 按照规划的步骤依次执行任务
3. 优先使用终端指令和快捷键来实现操作
4. 使用AutoOmni工具进行复杂的系统操作
5. 使用desktop-commander工具进行桌面和GUI操作
6. 记录每个步骤的执行结果和状态

执行状态报告格式：
[执行状态]
- 当前步骤: ...
- 执行结果: 成功/失败/进行中
- 详细信息: ...
- 下一步骤: ...
    """,
    tools=[
        MCPToolset(
            connection_params=StdioServerParameters(
                command='python',
                args=[
                    'c:/dev/MCP/AutoOmni/auto_omni_mcp.py'
                ],
            ),
        ),
        MCPToolset(
            connection_params=StdioServerParameters(
                command='npx',
                args=[
                    '-y',
                    '@wonderwhy-er/desktop-commander'
                ],
            ),
        )
    ],
    output_key="execution_result"
)

# Agent3: 任务检查智能体
task_checker = LlmAgent(
    model='gemini-1.5-flash-latest',
    name='task_checker_agent',
    description='任务检查智能体，负责验证任务执行结果',
    instruction="""
你是一个专业的任务检查智能体。你的主要职责是：

1. 接收来自任务规划智能体的检查指标
2. 接收来自任务执行智能体的执行结果信息
3. 根据检查指标验证任务是否真正完成

检查报告格式：
[检查结果]
- 总体状态: 完全完成/部分完成/未完成
- 指标检查详情:
  * 指标1: [完成]/[未完成] - 详细说明
  * 指标2: [完成]/[未完成] - 详细说明
  ...
    """,
    output_key="check_result"
)

# Agent4: 错误处理智能体 - 使用ai_knowledge_bridge
error_handler = LlmAgent(
    model='gemini-1.5-flash-latest',
    name='error_handler_agent',
    description='错误处理智能体，负责分析错误和不足，制定修复方案',
    instruction="""
你是一个专业的错误处理智能体。你的主要职责是：

1. 接收来自检查智能体的错误报告和未完成项目
2. 接收来自执行智能体的执行错误信息
3. 使用ai_knowledge_bridge工具分析错误原因和解决方案
4. 制定详细的修复计划

修复方案格式：
[错误分析]
- 错误类型: ...
- 根本原因: ...
- 影响范围: ...

[修复方案]
- 推荐方案: ...
- 修复步骤:
  1. ...
  2. ...
- 预期结果: ...
    """,
    tools=[
        MCPToolset(
            connection_params=StdioServerParameters(
                command='python',
                args=[
                    'c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py'
                ],
            ),
        )
    ],
    output_key="error_fix_plan"
)

# 创建工作流程
mcp_workflow = SequentialAgent(
    name="MCPWorkflow",
    sub_agents=[
        task_planner,
        task_executor,
        task_checker,
        error_handler,
    ]
)

# 创建根智能体
root_agent = LlmAgent(
    model='gemini-1.5-flash-latest',
    name='mcp_multi_agent_coordinator',
    description='带MCP工具的多智能体系统协调器，负责协调任务规划、执行、检查和错误处理的整个流程',
    instruction="""
你是多智能体系统的协调器，集成了强大的MCP工具。你的职责是：

1. 接收用户的任务需求
2. 协调各个专门智能体完成任务：
   - 任务规划智能体 (使用ai_knowledge_bridge获取专业知识)
   - 任务执行智能体 (使用AutoOmni和desktop-commander执行操作)
   - 任务检查智能体 (验证执行结果)
   - 错误处理智能体 (使用ai_knowledge_bridge分析和修复错误)

3. 确保任务高质量完成

请始终保持专业、高效，充分利用MCP工具的强大功能。
    """,
    sub_agents=[mcp_workflow]
)
