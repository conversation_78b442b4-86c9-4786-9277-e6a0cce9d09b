# Copyright 2025 Multi-Agent System
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.adk.agents.llm_agent import LlmAgent
from google.adk.agents.sequential_agent import SequentialAgent
from google.adk.agents.loop_agent import LoopAgent
from google.adk.agents.base_agent import BaseAgent
from google.adk.events import Event, EventActions
from google.adk.agents.invocation_context import InvocationContext
from typing import AsyncGenerator

from .agents.task_planner import TaskPlannerAgent
from .agents.task_executor import TaskExecutorAgent
from .agents.task_checker import TaskCheckerAgent
from .agents.error_handler import ErrorHandlerAgent


class TaskCompletionChecker(BaseAgent):
    """
    自定义智能体，用于检查任务是否完全完成
    如果任务完成，则停止循环；如果未完成，则继续循环
    """
    
    def __init__(self, name: str = "TaskCompletionChecker"):
        super().__init__(name=name)
    
    async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
        # 从状态中获取检查结果
        check_result = ctx.session.state.get("check_result", "")
        
        # 判断任务是否完全完成
        is_completed = "完全完成" in check_result or "总体状态: 完全完成" in check_result
        
        # 如果完成，则escalate停止循环；否则继续
        yield Event(
            author=self.name, 
            content=f"任务完成状态检查: {'已完成' if is_completed else '需要继续'}",
            actions=EventActions(escalate=is_completed)
        )


# 创建各个专门的智能体实例
task_planner = TaskPlannerAgent.create_agent()
task_executor = TaskExecutorAgent.create_agent()
task_checker = TaskCheckerAgent.create_agent()
error_handler = ErrorHandlerAgent.create_agent()

# 创建任务完成检查器
completion_checker = TaskCompletionChecker()

# 创建执行-检查-修复的循环流程
execution_loop = LoopAgent(
    name="ExecutionLoop",
    max_iterations=5,  # 最多重试5次
    sub_agents=[
        task_executor,      # 执行任务
        task_checker,       # 检查结果
        completion_checker, # 检查是否完成（如果完成则停止循环）
        error_handler,      # 如果未完成，处理错误并制定修复方案
    ]
)

# 创建主要的工作流程：规划 -> 执行循环
main_workflow = SequentialAgent(
    name="MainWorkflow",
    sub_agents=[
        task_planner,    # 首先进行任务规划
        execution_loop,  # 然后进入执行-检查-修复循环
    ]
)

# 创建根智能体作为系统的入口点
root_agent = LlmAgent(
    model='gemini-1.5-flash-latest',
    name='multi_agent_system_coordinator',
    description='多智能体系统协调器，负责协调任务规划、执行、检查和错误处理的整个流程',
    instruction="""
你是多智能体系统的协调器。你的职责是：

1. 接收用户的任务需求
2. 协调各个专门智能体完成任务
3. 确保任务按照正确的流程执行：
   - 任务规划 (task_planner_agent)
   - 任务执行 (task_executor_agent) 
   - 任务检查 (task_checker_agent)
   - 错误处理 (error_handler_agent，如需要)
   - 重复执行直到任务完成

4. 向用户报告最终结果

系统工作流程：
1. 用户输入 -> 任务规划智能体分析并制定计划
2. 执行智能体按计划执行任务
3. 检查智能体验证执行结果
4. 如果未完成，错误处理智能体制定修复方案
5. 重复步骤2-4直到任务完成或达到最大重试次数

请始终保持专业、高效，并确保用户了解当前的执行状态。
    """,
    sub_agents=[main_workflow]
)
