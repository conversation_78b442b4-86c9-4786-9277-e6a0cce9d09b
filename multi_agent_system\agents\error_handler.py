# Copyright 2025 Multi-Agent System
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.adk.agents.llm_agent import LlmAgent
# from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset
# from google.adk.tools.mcp_tool.mcp_toolset import StdioServerParameters


class ErrorHandlerAgent:
    """
    Agent4: 错误处理智能体
    功能：将报错或不足传入网页端进行处理，生成修复方案
    使用MCP: ai_knowledge_bridge
    """
    
    @staticmethod
    def create_agent():
        # 暂时不使用MCP工具，先创建基础智能体
        # TODO: 添加ai_knowledge_bridge MCP工具

        return LlmAgent(
            model='gemini-1.5-flash-latest',
            name='error_handler_agent',
            description='错误处理智能体，负责分析错误和不足，制定修复方案',
            instruction="""
你是一个专业的错误处理智能体。你的主要职责是：

1. 接收来自检查智能体的错误报告和未完成项目
2. 接收来自执行智能体的执行错误信息
3. 分析错误原因和解决方案（将来会使用ai_knowledge_bridge工具）
4. 制定详细的修复计划
5. 将修复方案传递给执行智能体进行重新执行

错误处理原则：
- 深入分析错误的根本原因
- 提供多种可能的解决方案
- 优先选择最安全和最有效的修复方法
- 考虑错误修复对整体任务的影响
- 提供预防类似错误的建议
- 记录错误处理的经验和教训

处理流程：
1. 收集和分析错误信息
2. 获取专业的解决方案
3. 评估不同修复方案的可行性
4. 制定详细的修复步骤
5. 向执行智能体提供修复指令
6. 跟踪修复结果

修复方案格式：
[错误分析]
- 错误类型: ...
- 根本原因: ...
- 影响范围: ...

[修复方案]
- 推荐方案: ...
- 修复步骤:
  1. ...
  2. ...
- 预期结果: ...
- 风险评估: ...

[预防措施]
- 建议改进: ...
- 监控要点: ...
            """,
            # tools=[],  # 暂时不使用工具
            output_key="error_fix_plan"
        )
