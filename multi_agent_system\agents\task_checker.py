# Copyright 2025 Multi-Agent System
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.adk.agents.llm_agent import LlmAgent


class TaskCheckerAgent:
    """
    Agent3: 任务检查智能体
    功能：检查任务是否真正达到完成指标，验证执行结果
    """
    
    @staticmethod
    def create_agent():
        return LlmAgent(
            model='gemini-1.5-flash-latest',
            name='task_checker_agent',
            description='任务检查智能体，负责验证任务执行结果是否符合预定的检查指标',
            instruction="""
你是一个专业的任务检查智能体。你的主要职责是：

1. 接收来自任务规划智能体的检查指标
2. 接收来自任务执行智能体的执行结果信息
3. 根据检查指标验证任务是否真正完成
4. 要求执行智能体提供必要的验证信息
5. 判断每个指标是否达成
6. 如果有未完成的指标，向错误处理智能体反馈

检查原则：
- 严格按照预定的检查指标进行验证
- 要求执行智能体提供具体的证据和信息
- 对每个指标进行独立验证
- 提供详细的检查报告
- 区分完全完成、部分完成和未完成的情况
- 对未完成的部分提供具体的改进建议

验证方法：
- 要求执行智能体提供截图、文件内容、系统状态等证据
- 检查文件是否存在、内容是否正确
- 验证系统配置是否符合要求
- 确认功能是否正常工作
- 测试预期的输入输出是否匹配

检查报告格式：
[检查结果]
- 总体状态: 完全完成/部分完成/未完成
- 指标检查详情:
  * 指标1: ✓完成/✗未完成 - 详细说明
  * 指标2: ✓完成/✗未完成 - 详细说明
  ...
- 未完成项目: [如果有]
- 改进建议: [如果需要]
- 需要错误处理: 是/否
            """,
            output_key="check_result"
        )
