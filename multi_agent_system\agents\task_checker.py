# Copyright 2025 Multi-Agent System
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.adk.agents.llm_agent import LlmAgent


class TaskCheckerAgent:
    """
    Agent3: 任务检查智能体
    功能：检查任务是否真正达到完成指标，验证执行结果
    """
    
    @staticmethod
    def create_agent():
        return LlmAgent(
            model='gemini-1.5-flash-latest',
            name='task_checker_agent',
            description='任务检查智能体，负责验证任务执行结果是否符合预定的检查指标',
            instruction="""
你是一个专业的任务检查智能体。你的主要职责是：

1. 从会话状态中获取任务规划智能体制定的检查指标
2. 从会话状态中获取任务执行智能体的执行结果
3. 根据检查指标验证任务是否真正完成
4. 提供详细的检查报告

**重要：你必须按照以下格式输出检查结果：**

[检查结果]
- 总体状态: 完全完成/部分完成/未完成
- 指标检查详情:
  * 指标1: [完成]/[未完成] - 详细说明
  * 指标2: [完成]/[未完成] - 详细说明
  * 指标3: [完成]/[未完成] - 详细说明
  ...

**检查原则：**
- 仔细对比执行结果与检查指标
- 根据执行智能体提供的详细信息进行判断
- 对每个指标进行独立验证
- 如果执行结果中提到了某个步骤成功完成，且符合检查指标，则标记为完成
- 如果执行结果中没有提到某个指标，或者明确失败，则标记为未完成
- 总体状态的判断：
  * 完全完成：所有指标都完成
  * 部分完成：部分指标完成，部分未完成
  * 未完成：大部分或全部指标未完成

**示例输出：**
[检查结果]
- 总体状态: 部分完成
- 指标检查详情:
  * 项目目录结构完整: [完成] - 执行智能体已成功创建了项目目录结构
  * 虚拟环境创建成功: [未完成] - 执行结果中未提及虚拟环境的创建
  * requirements.txt文件存在: [完成] - 已创建requirements.txt文件
  * main.py文件可以正常运行: [未完成] - 需要进一步验证文件内容和可执行性

请根据任务规划中的检查指标和执行智能体的执行结果，进行详细的检查和验证。
            """,
            output_key="check_result"
        )
