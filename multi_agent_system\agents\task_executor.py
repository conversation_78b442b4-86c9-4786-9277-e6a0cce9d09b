# Copyright 2025 Multi-Agent System
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.adk.agents.llm_agent import LlmAgent
# from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset
# from google.adk.tools.mcp_tool.mcp_toolset import StdioServerParameters


class TaskExecutorAgent:
    """
    Agent2: 任务执行智能体
    功能：将任务规划依次执行，控制终端、桌面操作、文件系统操作
    使用MCP: AutoOmni, desktop-commander
    """
    
    @staticmethod
    def create_agent():
        # 暂时不使用MCP工具，先创建基础智能体
        # TODO: 添加AutoOmni和desktop-commander MCP工具

        return LlmAgent(
            model='gemini-1.5-flash-latest',
            name='task_executor_agent',
            description='任务执行智能体，负责执行具体的任务步骤，包括终端操作、桌面操作和文件系统操作',
            instruction="""
你是一个专业的任务执行智能体。你的主要职责是：

1. 接收来自任务规划智能体的详细任务规划
2. 按照规划的步骤依次执行任务
3. 优先使用终端指令和快捷键来实现操作
4. 进行复杂的系统操作（将来会使用AutoOmni工具）
5. 进行桌面和GUI操作（将来会使用desktop-commander工具）
6. 记录每个步骤的执行结果和状态
7. 向检查智能体提供执行信息以供验证

执行原则：
- 严格按照任务规划的顺序执行
- 优先使用命令行和快捷键，提高执行效率
- 对每个操作进行适当的错误处理
- 详细记录执行过程和结果
- 在遇到问题时及时报告给错误处理智能体
- 确保操作的安全性，避免破坏性操作

工具使用指南：
- 系统级操作: 文件管理、进程控制等
- 桌面GUI操作: 窗口管理、鼠标键盘模拟等

执行状态报告格式：
[执行状态]
- 当前步骤: ...
- 执行结果: 成功/失败/进行中
- 详细信息: ...
- 下一步骤: ...
            """,
            # tools=[],  # 暂时不使用工具
            output_key="execution_result"
        )
