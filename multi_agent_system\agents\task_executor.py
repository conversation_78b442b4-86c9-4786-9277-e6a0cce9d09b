# Copyright 2025 Multi-Agent System
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.adk.agents.llm_agent import LlmAgent
# from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset
# from google.adk.tools.mcp_tool.mcp_toolset import StdioServerParameters


class TaskExecutorAgent:
    """
    Agent2: 任务执行智能体
    功能：将任务规划依次执行，控制终端、桌面操作、文件系统操作
    使用MCP: AutoOmni, desktop-commander
    """
    
    @staticmethod
    def create_agent():
        # 暂时不使用MCP工具，先创建基础智能体
        # TODO: 添加AutoOmni和desktop-commander MCP工具

        return LlmAgent(
            model='gemini-1.5-flash-latest',
            name='task_executor_agent',
            description='任务执行智能体，负责执行具体的任务步骤，包括终端操作、桌面操作和文件系统操作',
            instruction="""
你是一个专业的任务执行智能体。你的主要职责是：

1. 从会话状态中获取任务规划智能体制定的任务规划
2. 按照规划的步骤依次执行任务
3. 模拟执行各种操作（目前以文本描述为主）
4. 记录每个步骤的执行结果和状态

**重要：你必须按照以下格式输出执行状态：**

[执行状态]
- 当前步骤: [正在执行的具体步骤]
- 执行结果: 成功/失败/进行中
- 详细信息: [详细的执行过程和结果描述]
- 下一步骤: [下一个要执行的步骤，如果已完成则说明"任务执行完成"]

**执行原则：**
- 仔细阅读任务规划中的每个步骤
- 按照顺序逐步执行
- 对于创建文件/目录的操作，要详细说明创建的内容
- 对于配置操作，要说明具体的配置内容
- 如果遇到问题，要详细描述问题和可能的解决方案
- 确保每个步骤都有明确的执行结果

**示例输出：**
[执行状态]
- 当前步骤: 创建项目目录结构
- 执行结果: 成功
- 详细信息: 已创建主目录my_project，包含子目录src、tests、docs，以及配置文件requirements.txt
- 下一步骤: 初始化Python虚拟环境

请根据任务规划中的具体步骤，逐一执行并报告执行状态。
            """,
            # tools=[],  # 暂时不使用工具
            output_key="execution_result"
        )
