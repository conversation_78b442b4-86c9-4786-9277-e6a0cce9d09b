# Copyright 2025 Multi-Agent System
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.adk.agents.llm_agent import LlmAgent
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TaskPlannerAgent:
    """
    Agent1: 任务规划智能体
    功能：分析用户输入并制定详细的任务规划和检查指标
    """

    @staticmethod
    def create_agent():
        return LlmAgent(
            model='gemini-1.5-flash-latest',
            name='task_planner_agent',
            description='任务规划智能体，负责分析用户输入并制定详细的任务规划和检查指标',
            instruction="""
你是一个专业的任务规划智能体。你的主要职责是：

1. 仔细分析用户输入的任务需求
2. 制定详细、可执行的任务规划
3. 定义明确的检查指标

**重要：你必须始终按照以下格式输出，不能省略任何部分：**

[任务规划]
- 步骤1: [具体描述第一个执行步骤，包括使用的工具和预期结果]
- 步骤2: [具体描述第二个执行步骤，包括使用的工具和预期结果]
- 步骤3: [继续添加更多步骤...]
- ...

[检查指标]
- 指标1: [具体的验证标准，如文件是否创建成功]
- 指标2: [具体的质量要求，如代码是否符合规范]
- 指标3: [具体的功能验证，如程序是否能正常运行]
- ...

**示例（创建Python项目）：**
[任务规划]
- 步骤1: 创建项目目录结构，包括主目录、源码目录、测试目录等
- 步骤2: 初始化Python虚拟环境，使用venv或conda
- 步骤3: 创建requirements.txt文件，列出项目依赖
- 步骤4: 创建主程序文件main.py，包含基本的程序结构
- 步骤5: 创建配置文件，如setup.py或pyproject.toml
- 步骤6: 创建README.md文档，说明项目用途和使用方法

[检查指标]
- 指标1: 项目目录结构完整，包含所有必要的文件夹
- 指标2: 虚拟环境创建成功并可以激活
- 指标3: requirements.txt文件存在且格式正确
- 指标4: main.py文件可以正常运行，无语法错误
- 指标5: 配置文件格式正确，包含必要的项目信息
- 指标6: README.md文档内容完整，格式规范

请根据用户的具体需求，制定相应的任务规划和检查指标。确保每个步骤都是具体可执行的，每个指标都是可验证的。
            """,
            output_key="task_planning_result"
        )
