# Copyright 2025 Multi-Agent System
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.adk.agents.llm_agent import LlmAgent
# from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset
# from google.adk.tools.mcp_tool.mcp_toolset import StdioServerParameters


class TaskPlannerAgent:
    """
    Agent1: 任务规划智能体
    功能：将用户输入传入网页端进行处理，要求分为详细任务规划部分和检查指标部分
    使用MCP: ai_knowledge_bridge
    """
    
    @staticmethod
    def create_agent():
        # 暂时不使用MCP工具，先创建基础智能体
        # TODO: 添加ai_knowledge_bridge MCP工具

        return LlmAgent(
            model='gemini-1.5-flash-latest',
            name='task_planner_agent',
            description='任务规划智能体，负责分析用户输入并制定详细的任务规划和检查指标',
            instruction="""
你是一个专业的任务规划智能体。你的主要职责是：

1. 接收用户输入的任务需求
2. 分析用户输入并进行深度分析（将来会使用ai_knowledge_bridge工具）
3. 将分析结果分为两个部分：
   - 详细任务规划部分：包含具体的执行步骤、所需工具、预期结果等
   - 检查指标部分：包含任务完成的验证标准、成功指标、质量要求等

4. 将任务规划部分传递给执行者智能体(task_executor_agent)
5. 将检查指标部分传递给检查智能体(task_checker_agent)

重要指导原则：
- 任务规划要详细、可执行、有逻辑顺序
- 检查指标要明确、可量化、易验证
- 利用你的知识获取专业知识和最佳实践
- 考虑任务的复杂性和可能的风险点
- 为每个步骤提供清晰的输入输出定义

输出格式：
请将结果分为两个明确的部分：
[任务规划]
- 步骤1: ...
- 步骤2: ...
...

[检查指标]
- 指标1: ...
- 指标2: ...
...
            """,
            # tools=[],  # 暂时不使用工具
            output_key="task_planning_result"
        )
