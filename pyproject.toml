[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "multi-agent-system"
version = "0.1.0"
description = "Multi-Agent System using Google ADK with MCP tools"
authors = [
    {name = "Developer", email = "<EMAIL>"}
]
dependencies = [
    "google-adk",
    "python-dotenv",
    "pydantic",
]
requires-python = ">=3.8"

[project.optional-dependencies]
dev = [
    "pytest",
    "black",
    "flake8",
]

[tool.setuptools.packages.find]
where = ["."]
include = ["multi_agent_system*", "app*"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
