#!/usr/bin/env python3
"""
MCP工具设置脚本
帮助用户配置和验证MCP工具的安装
"""

import os
import json
import subprocess
import sys
from pathlib import Path


def check_python():
    """检查Python环境"""
    print("🔍 检查Python环境...")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    return True


def check_nodejs():
    """检查Node.js环境"""
    print("\n🔍 检查Node.js环境...")
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js未安装或不在PATH中")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装")
        return False


def check_npx():
    """检查npx"""
    print("\n🔍 检查npx...")
    try:
        result = subprocess.run(["npx", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npx版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ npx不可用")
            return False
    except FileNotFoundError:
        print("❌ npx未找到")
        return False


def check_mcp_paths():
    """检查MCP工具路径"""
    print("\n🔍 检查MCP工具路径...")
    
    # 从要求.txt中提取的路径
    mcp_paths = {
        "ai_knowledge_bridge": "c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py",
        "auto_omni": "c:/dev/MCP/AutoOmni/auto_omni_mcp.py"
    }
    
    results = {}
    for name, path in mcp_paths.items():
        if os.path.exists(path):
            print(f"✅ {name}: {path}")
            results[name] = True
        else:
            print(f"❌ {name}: {path} (文件不存在)")
            results[name] = False
    
    return results


def install_desktop_commander():
    """安装desktop-commander"""
    print("\n🔧 安装desktop-commander...")
    try:
        result = subprocess.run(
            ["npx", "-y", "@wonderwhy-er/desktop-commander", "--help"],
            capture_output=True,
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ desktop-commander安装/验证成功")
            return True
        else:
            print(f"❌ desktop-commander安装失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⚠️  desktop-commander安装超时，但可能已成功")
        return True
    except Exception as e:
        print(f"❌ desktop-commander安装出错: {e}")
        return False


def generate_mcp_config():
    """生成MCP配置文件"""
    print("\n📝 生成MCP配置...")
    
    config = {
        "mcpServers": {
            "desktop-commander": {
                "command": "npx",
                "args": [
                    "-y",
                    "@wonderwhy-er/desktop-commander"
                ]
            },
            "ai_knowledge_bridge": {
                "runtime": "python",
                "command": "python",
                "args": [
                    "c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py"
                ]
            },
            "auto-omni": {
                "runtime": "python",
                "command": "python",
                "args": [
                    "c:/dev/MCP/AutoOmni/auto_omni_mcp.py"
                ],
                "port": 5001
            }
        }
    }
    
    config_file = "mcp_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ MCP配置已保存到: {config_file}")
    return config_file


def create_env_file():
    """创建.env文件"""
    print("\n📝 创建环境配置文件...")
    
    env_path = Path("app/.env")
    if env_path.exists():
        print("⚠️  app/.env文件已存在，跳过创建")
        return str(env_path)
    
    env_example_path = Path("app/.env.example")
    if env_example_path.exists():
        # 复制示例文件
        with open(env_example_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已从示例文件创建: {env_path}")
        print("⚠️  请编辑该文件并填入您的Google API密钥")
    else:
        print("❌ 未找到.env.example文件")
    
    return str(env_path)


def main():
    """主函数"""
    print("🚀 MCP工具设置向导")
    print("=" * 50)
    
    # 检查基础环境
    checks = [
        ("Python环境", check_python),
        ("Node.js环境", check_nodejs),
        ("npx工具", check_npx),
    ]
    
    for check_name, check_func in checks:
        if not check_func():
            print(f"\n❌ {check_name}检查失败，请先安装相关依赖")
            return
    
    # 检查MCP工具路径
    mcp_results = check_mcp_paths()
    
    # 安装desktop-commander
    if check_nodejs() and check_npx():
        install_desktop_commander()
    
    # 生成配置文件
    config_file = generate_mcp_config()
    env_file = create_env_file()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 设置总结:")
    print("=" * 50)
    
    print("✅ 已完成的设置:")
    print(f"   - MCP配置文件: {config_file}")
    print(f"   - 环境配置文件: {env_file}")
    
    if not all(mcp_results.values()):
        print("\n⚠️  需要手动设置的MCP工具:")
        for name, status in mcp_results.items():
            if not status:
                print(f"   - {name}: 请确保相关工具已安装并路径正确")
    
    print("\n💡 下一步:")
    print("   1. 编辑 app/.env 文件，填入您的Google API密钥")
    print("   2. 确保所有MCP工具已正确安装")
    print("   3. 运行测试: python test_system.py")
    print("   4. 启动系统: cd app && python main.py")
    
    print("\n🔗 相关链接:")
    print("   - Google AI Studio: https://aistudio.google.com/app/apikey")
    print("   - ADK文档: https://google.github.io/adk-docs/")


if __name__ == "__main__":
    main()
