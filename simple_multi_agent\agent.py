# Copyright 2025 Multi-Agent System
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.adk.agents.llm_agent import LlmAgent
from google.adk.agents.sequential_agent import SequentialAgent

# 创建简化的智能体，不使用MCP工具
task_planner = LlmAgent(
    model='gemini-1.5-flash-latest',
    name='task_planner_agent',
    description='任务规划智能体，负责分析用户输入并制定详细的任务规划和检查指标',
    instruction="""
你是一个专业的任务规划智能体。你的主要职责是：

1. 接收用户输入的任务需求
2. 分析用户输入并进行深度分析
3. 将分析结果分为两个部分：
   - 详细任务规划部分：包含具体的执行步骤、所需工具、预期结果等
   - 检查指标部分：包含任务完成的验证标准、成功指标、质量要求等

输出格式：
请将结果分为两个明确的部分：
[任务规划]
- 步骤1: ...
- 步骤2: ...
...

[检查指标]  
- 指标1: ...
- 指标2: ...
...
    """,
    output_key="task_planning_result"
)

task_executor = LlmAgent(
    model='gemini-1.5-flash-latest',
    name='task_executor_agent',
    description='任务执行智能体，负责执行具体的任务步骤',
    instruction="""
你是一个专业的任务执行智能体。你的主要职责是：

1. 接收来自任务规划智能体的详细任务规划
2. 按照规划的步骤依次执行任务
3. 记录每个步骤的执行结果和状态

执行状态报告格式：
[执行状态]
- 当前步骤: ...
- 执行结果: 成功/失败/进行中
- 详细信息: ...
- 下一步骤: ...
    """,
    output_key="execution_result"
)

task_checker = LlmAgent(
    model='gemini-1.5-flash-latest',
    name='task_checker_agent',
    description='任务检查智能体，负责验证任务执行结果',
    instruction="""
你是一个专业的任务检查智能体。你的主要职责是：

1. 接收来自任务规划智能体的检查指标
2. 接收来自任务执行智能体的执行结果信息
3. 根据检查指标验证任务是否真正完成

检查报告格式：
[检查结果]
- 总体状态: 完全完成/部分完成/未完成
- 指标检查详情:
  * 指标1: [完成]/[未完成] - 详细说明
  * 指标2: [完成]/[未完成] - 详细说明
  ...
    """,
    output_key="check_result"
)

# 创建简单的顺序工作流
simple_workflow = SequentialAgent(
    name="SimpleWorkflow",
    sub_agents=[
        task_planner,
        task_executor,
        task_checker,
    ]
)

# 创建根智能体
root_agent = LlmAgent(
    model='gemini-1.5-flash-latest',
    name='simple_multi_agent_coordinator',
    description='简化多智能体系统协调器，负责协调任务规划、执行和检查的整个流程',
    instruction="""
你是多智能体系统的协调器。你的职责是：

1. 接收用户的任务需求
2. 协调各个专门智能体完成任务
3. 确保任务按照正确的流程执行：
   - 任务规划 (task_planner_agent)
   - 任务执行 (task_executor_agent) 
   - 任务检查 (task_checker_agent)

4. 向用户报告最终结果

请始终保持专业、高效，并确保用户了解当前的执行状态。
    """,
    sub_agents=[simple_workflow]
)
