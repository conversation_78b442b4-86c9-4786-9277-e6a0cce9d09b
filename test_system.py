#!/usr/bin/env python3
"""
多智能体系统测试脚本
用于验证系统的基本功能和配置
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否正常"""
    print("🔍 测试模块导入...")
    
    try:
        from google.adk.agents.llm_agent import LlmAgent
        print("✅ Google ADK LlmAgent 导入成功")
    except ImportError as e:
        print(f"❌ Google ADK 导入失败: {e}")
        return False
    
    try:
        from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset
        print("✅ MCP Toolset 导入成功")
    except ImportError as e:
        print(f"❌ MCP Toolset 导入失败: {e}")
        return False
    
    try:
        from multi_agent_system.agent import root_agent
        print("✅ 多智能体系统 导入成功")
    except ImportError as e:
        print(f"❌ 多智能体系统 导入失败: {e}")
        return False
    
    return True


def test_environment():
    """测试环境配置"""
    print("\n🔍 测试环境配置...")
    
    # 加载环境变量
    load_dotenv("app/.env")
    
    # 检查必要的环境变量
    required_vars = ["GOOGLE_API_KEY"]
    optional_vars = ["AI_KNOWLEDGE_BRIDGE_PATH", "AUTO_OMNI_PATH"]
    
    all_good = True
    
    for var in required_vars:
        if os.getenv(var):
            print(f"✅ {var} 已设置")
        else:
            print(f"❌ {var} 未设置 (必需)")
            all_good = False
    
    for var in optional_vars:
        if os.getenv(var):
            print(f"✅ {var} 已设置")
        else:
            print(f"⚠️  {var} 未设置 (可选)")
    
    return all_good


def test_agent_creation():
    """测试智能体创建"""
    print("\n🔍 测试智能体创建...")
    
    try:
        from multi_agent_system.agents.task_planner import TaskPlannerAgent
        agent1 = TaskPlannerAgent.create_agent()
        print("✅ 任务规划智能体创建成功")
    except Exception as e:
        print(f"❌ 任务规划智能体创建失败: {e}")
        return False
    
    try:
        from multi_agent_system.agents.task_executor import TaskExecutorAgent
        agent2 = TaskExecutorAgent.create_agent()
        print("✅ 任务执行智能体创建成功")
    except Exception as e:
        print(f"❌ 任务执行智能体创建失败: {e}")
        return False
    
    try:
        from multi_agent_system.agents.task_checker import TaskCheckerAgent
        agent3 = TaskCheckerAgent.create_agent()
        print("✅ 任务检查智能体创建成功")
    except Exception as e:
        print(f"❌ 任务检查智能体创建失败: {e}")
        return False
    
    try:
        from multi_agent_system.agents.error_handler import ErrorHandlerAgent
        agent4 = ErrorHandlerAgent.create_agent()
        print("✅ 错误处理智能体创建成功")
    except Exception as e:
        print(f"❌ 错误处理智能体创建失败: {e}")
        return False
    
    return True


async def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试基本功能...")
    
    try:
        from google.adk.core.runner import Runner
        from google.adk.core.session_service import InMemorySessionService
        from multi_agent_system.agent import root_agent
        
        # 设置会话管理
        session_service = InMemorySessionService()
        session = session_service.create_session(
            app_name="test_app", user_id="test_user", session_id="test_session"
        )
        
        # 设置智能体运行器
        runner = Runner(
            agent=root_agent, app_name="test_app", session_service=session_service
        )
        
        print("✅ 系统初始化成功")
        print("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 多智能体系统测试")
    print("=" * 50)
    
    # 运行各项测试
    tests = [
        ("模块导入", test_imports),
        ("环境配置", test_environment),
        ("智能体创建", test_agent_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    # 异步测试
    print("\n🔍 运行异步测试...")
    try:
        async_result = asyncio.run(test_basic_functionality())
        results.append(("基本功能", async_result))
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        results.append(("基本功能", False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有测试通过！系统可以正常使用。")
        print("\n💡 下一步:")
        print("   1. 确保MCP工具已正确安装")
        print("   2. 运行 'cd app && python main.py' 启动系统")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖项。")
        print("\n🔧 建议:")
        print("   1. 检查 app/.env 文件中的配置")
        print("   2. 确保已安装所有依赖项: pip install -r requirements.txt")
        print("   3. 验证Google API密钥的有效性")


if __name__ == "__main__":
    main()
